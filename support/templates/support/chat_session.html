<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Support Chat - {{ session.subject|default:"Support Session" }}</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-100">
    <div class="max-w-4xl mx-auto p-4">
        <!-- Header -->
        <div class="bg-white rounded-lg shadow-lg p-6 mb-6">
            <div class="flex items-center justify-between">
                <div>
                    <h1 class="text-2xl font-bold text-gray-800">Support Chat</h1>
                    <p class="text-gray-600">Session ID: {{ session.id }}</p>
                </div>
                <div class="text-right">
                    <div class="flex items-center space-x-2">
                        <span class="px-3 py-1 rounded-full text-sm {{ session.is_active|yesno:'bg-green-100 text-green-800,bg-gray-100 text-gray-600' }}">
                            {{ session.is_active|yesno:"Active,Closed" }}
                        </span>
                    </div>
                    <p class="text-sm text-gray-500 mt-1">
                        Created: {{ session.created_at|date:"M d, Y H:i" }}
                    </p>
                </div>
            </div>
            
            {% if session.subject %}
            <div class="mt-4 p-3 bg-blue-50 rounded-lg">
                <p class="text-sm text-blue-800"><strong>Subject:</strong> {{ session.subject }}</p>
            </div>
            {% endif %}
            
            {% if session.page_url %}
            <div class="mt-2 p-3 bg-gray-50 rounded-lg">
                <p class="text-sm text-gray-700"><strong>Page:</strong> <a href="{{ session.page_url }}" class="text-blue-600 hover:underline" target="_blank">{{ session.page_url }}</a></p>
            </div>
            {% endif %}
        </div>

        <!-- Messages -->
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h2 class="text-xl font-semibold mb-4">Conversation</h2>
            
            <div id="messages-container" class="space-y-4 max-h-96 overflow-y-auto">
                {% for message in session.messages.all %}
                <div class="flex {{ message.sender|yesno:'justify-end,justify-start' }}">
                    <div class="max-w-xs lg:max-w-md px-4 py-2 rounded-lg {{ message.sender|yesno:'bg-blue-600 text-white,bg-gray-200 text-gray-800' }}">
                        <p class="text-sm">{{ message.content|linebreaks }}</p>
                        <div class="flex items-center justify-between mt-2 text-xs {{ message.sender|yesno:'text-blue-200,text-gray-500' }}">
                            <span>
                                {% if message.sender == 'support' and message.support_agent_name %}
                                    {{ message.support_agent_name }}
                                {% else %}
                                    {{ message.sender|capfirst }}
                                {% endif %}
                            </span>
                            <span>{{ message.created_at|date:"M d, H:i" }}</span>
                        </div>
                    </div>
                </div>
                {% empty %}
                <div class="text-center py-8">
                    <p class="text-gray-500">No messages in this conversation yet.</p>
                </div>
                {% endfor %}
            </div>
            
            <!-- Auto-refresh notice -->
            <div class="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <p class="text-sm text-yellow-800">
                    💡 <strong>Tip:</strong> This page shows a snapshot of your conversation. 
                    For real-time chat, use the chat widget on any page of the site.
                </p>
            </div>
        </div>

        <!-- Actions -->
        <div class="mt-6 flex space-x-4">
            <a href="/" class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                Back to Site
            </a>
            <button onclick="openChatWidget()" class="bg-green-600 text-white px-6 py-2 rounded-lg hover:bg-green-700 transition-colors">
                Continue Chat
            </button>
            <button onclick="window.print()" class="bg-gray-600 text-white px-6 py-2 rounded-lg hover:bg-gray-700 transition-colors">
                Print Conversation
            </button>
        </div>
    </div>

    <script>
        function openChatWidget() {
            // Redirect to a page with the chat widget and auto-open this session
            window.location.href = '/?open_chat={{ session.id }}';
        }
        
        // Auto-scroll to bottom of messages
        document.addEventListener('DOMContentLoaded', function() {
            const container = document.getElementById('messages-container');
            container.scrollTop = container.scrollHeight;
        });
    </script>
</body>
</html>
