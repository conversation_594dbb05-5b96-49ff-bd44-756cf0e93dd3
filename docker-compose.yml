version: '3.8'
services:
  web:
    build: .
    ports:
      - "8010:8000"
    env_file:
      - .env
    environment:
      - PYTHONDONTWRITEBYTECODE=1
      - PYTHONUNBUFFERED=1
    command: >
      sh -c "python manage.py collectstatic --noinput &&
            python manage.py makemigrations &&
            python manage.py makemigrations clients campaign api support &&
            python manage.py migrate clients &&
            python manage.py migrate campaign &&
            python manage.py migrate support &&
            python manage.py migrate &&
            gunicorn dcrm.wsgi:application --bind 0.0.0.0:8000 --workers 2"
    volumes:
      - .:/app
      - static_volume:/app/staticfiles
    depends_on:
      db:
        condition: service_healthy
    networks:
      - app-network

  db:
    image: postgres:15
    restart: always
    environment:
      POSTGRES_DB: vibereach-db-1
      POSTGRES_USER: omar
      POSTGRES_PASSWORD: uW9_qX9_pT8-yG4_zU1_
    volumes:
      - pgdata:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U omar -d vibereach-db-1"]
      interval: 5s
      timeout: 5s
      retries: 5
    networks:
      - app-network

volumes:
  static_volume:
  pgdata:

networks:
  app-network:
    driver: bridge
