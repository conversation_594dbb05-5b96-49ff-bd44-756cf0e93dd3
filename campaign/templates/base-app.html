{% extends "base.html" %}

{% block title %}{% endblock %}


{% block base-content %}
<div class="flex h-full font-sans">

    <!-- side-nav -->
    {% include "components/nav.html" %}

    <!-- Main Content Area -->
    <div class="flex w-full flex-col">
      <!-- Header -->
       {% include "components/header.html" %}
      
      
      <!-- Content -->
      <main class="flex-1 main-content-bg pl-[100px] pr-[100px] overflow-y-auto"> 
        {% block content %}{% endblock %}
        <!-- The main content is empty as per the screenshot -->
      </main>
    </div>

  </div>

  <!-- Include Shared Modals for entire app -->
  {% include 'components/modals.html' %}

  <!-- Include Chat Widget -->
  {% include 'components/chat-bubble.html' %}


<script>
  // Helper function to get CSRF token
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}
</script>  
{% endblock %}


